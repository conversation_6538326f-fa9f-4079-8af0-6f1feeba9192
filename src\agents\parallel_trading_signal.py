"""
平行线交易信号模型

定义了支持平行线交易功能的扩展信号格式，包含原有的分析信号和新增的交易决策信息。
"""

from pydantic import BaseModel, Field
from typing_extensions import Literal
from typing import Optional, Dict, Any


class ParallelTradingSignal(BaseModel):
    """
    平行线交易信号模型
    
    扩展了原有的分析信号，增加了具体的交易决策信息，
    支持代理在平行线交易中做出独立的交易决策。
    """
    # 原有分析信号
    signal: Literal["bullish", "bearish", "neutral"] = Field(
        description="市场信号：看涨、看跌或中性"
    )
    confidence: float = Field(
        description="信号置信度，范围0-100",
        ge=0, le=100
    )
    reasoning: str = Field(
        description="分析推理过程和依据"
    )
    
    # 新增交易决策信息
    trading_action: Literal["buy", "sell", "short", "cover", "hold"] = Field(
        description="具体交易动作：买入、卖出、做空、平仓或持有"
    )
    trading_quantity: int = Field(
        description="交易数量（股数），必须为正整数",
        ge=0
    )
    position_sizing: float = Field(
        description="建议仓位大小，范围0-1（0表示空仓，1表示满仓）",
        ge=0, le=1
    )
    
    # 可选的额外信息
    risk_assessment: Optional[str] = Field(
        default=None,
        description="风险评估说明"
    )
    time_horizon: Optional[Literal["short", "medium", "long"]] = Field(
        default=None,
        description="投资时间范围：短期、中期或长期"
    )


class LegacySignalAdapter:
    """
    传统信号适配器
    
    用于将现有的简单信号格式转换为平行线交易信号格式，
    确保向后兼容性。
    """
    
    @staticmethod
    def adapt_legacy_signal(
        signal: str,
        confidence: float,
        reasoning: str,
        current_price: float = 0,
        available_cash: float = 0,
        agent_name: str = "unknown"
    ) -> ParallelTradingSignal:
        """
        将传统信号转换为平行线交易信号
        
        Args:
            signal: 原始信号 (bullish/bearish/neutral)
            confidence: 置信度
            reasoning: 推理过程
            current_price: 当前股价
            available_cash: 可用现金
            agent_name: 代理名称
            
        Returns:
            ParallelTradingSignal: 转换后的平行线交易信号
        """
        # 基于信号和置信度确定交易动作
        if signal == "bullish" and confidence >= 60:
            trading_action = "buy"
            # 基于置信度确定仓位大小
            if confidence >= 80:
                position_sizing = 0.8  # 高置信度，大仓位
            elif confidence >= 70:
                position_sizing = 0.6  # 中等置信度，中等仓位
            else:
                position_sizing = 0.4  # 较低置信度，小仓位
        elif signal == "bearish" and confidence >= 60:
            # 看跌信号优先使用sell操作，系统会自动处理0仓位转做空的逻辑
            trading_action = "sell"  # 看跌时先尝试卖出，0仓位时会自动转为做空
            if confidence >= 80:
                position_sizing = 0.8  # 高置信度，大幅减仓或做空
            elif confidence >= 70:
                position_sizing = 0.6  # 中等置信度，中等减仓或做空
            else:
                position_sizing = 0.4  # 较低置信度，小幅减仓或做空
        else:
            trading_action = "hold"
            position_sizing = 0.0  # 持有时不改变仓位
        
        # 计算交易数量
        trading_quantity = 0
        if trading_action == "buy" and current_price > 0 and available_cash > 0:
            # 基于仓位大小和可用现金计算购买数量
            target_investment = available_cash * position_sizing
            trading_quantity = int(target_investment / current_price)
        elif trading_action == "short" and current_price > 0 and available_cash > 0:
            # 基于仓位大小和可用现金计算做空数量（考虑保证金要求）
            # 假设保证金要求为50%
            margin_requirement = 0.5
            available_margin = available_cash / margin_requirement
            target_short_value = available_margin * position_sizing
            trading_quantity = int(target_short_value / current_price)
        elif trading_action in ["sell", "cover"]:
            # 卖出和平仓时的数量需要在实际执行时根据持仓确定
            trading_quantity = 0  # 将在执行时动态计算
        
        # 生成风险评估
        risk_assessment = LegacySignalAdapter._generate_risk_assessment(
            signal, confidence, agent_name
        )
        
        return ParallelTradingSignal(
            signal=signal,
            confidence=confidence,
            reasoning=reasoning,
            trading_action=trading_action,
            trading_quantity=trading_quantity,
            position_sizing=position_sizing,
            risk_assessment=risk_assessment,
            time_horizon="medium"  # 默认中期投资
        )
    
    @staticmethod
    def _generate_risk_assessment(signal: str, confidence: float, agent_name: str) -> str:
        """生成风险评估说明"""
        if confidence >= 80:
            risk_level = "低风险"
        elif confidence >= 60:
            risk_level = "中等风险"
        else:
            risk_level = "高风险"
        
        return f"{agent_name}代理基于{signal}信号，置信度{confidence}%，评估为{risk_level}交易"


def convert_legacy_signals_to_parallel(
    legacy_signals: Dict[str, Dict[str, Any]],
    current_prices: Dict[str, float],
    available_cash: float
) -> Dict[str, Dict[str, ParallelTradingSignal]]:
    """
    批量转换传统信号为平行线交易信号
    
    Args:
        legacy_signals: 传统信号字典 {agent_name: {ticker: signal_data}}
        current_prices: 当前价格字典 {ticker: price}
        available_cash: 可用现金
        
    Returns:
        转换后的平行线交易信号字典
    """
    parallel_signals = {}
    
    for agent_name, agent_signals in legacy_signals.items():
        # 跳过不需要转换的代理
        if agent_name in ["risk_management_agent", "reflection_analyst"]:
            continue
            
        parallel_signals[agent_name] = {}
        
        for ticker, signal_data in agent_signals.items():
            if isinstance(signal_data, dict):
                signal = signal_data.get("signal", "neutral")
                confidence = signal_data.get("confidence", 0)
                reasoning = signal_data.get("reasoning", "")
                
                current_price = current_prices.get(ticker, 0)
                
                parallel_signal = LegacySignalAdapter.adapt_legacy_signal(
                    signal=signal,
                    confidence=confidence,
                    reasoning=reasoning,
                    current_price=current_price,
                    available_cash=available_cash,
                    agent_name=agent_name
                )
                
                parallel_signals[agent_name][ticker] = parallel_signal
    
    return parallel_signals

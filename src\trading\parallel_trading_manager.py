"""
平行线交易管理器

管理多条独立的交易线，每条线对应一个分析代理，
维护独立的投资组合状态和交易历史。
"""

import json
import os
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
import numpy as np
import shutil

from src.agents.parallel_trading_signal import ParallelTradingSignal


@dataclass
class TradeRecord:
    """交易记录"""
    date: str
    ticker: str
    action: str  # buy, sell, hold
    quantity: int
    price: float
    total_value: float
    agent_name: str
    reasoning: str = ""


@dataclass
class ParallelLinePortfolio:
    """单条平行线的投资组合状态"""
    agent_name: str
    initial_capital: float
    cash: float = 0.0
    positions: Dict[str, int] = field(default_factory=dict)  # ticker -> long quantity
    cost_basis: Dict[str, float] = field(default_factory=dict)  # ticker -> long total cost
    short_positions: Dict[str, int] = field(default_factory=dict)  # ticker -> short quantity
    short_cost_basis: Dict[str, float] = field(default_factory=dict)  # ticker -> short total cost
    margin_used: float = 0.0  # 总保证金使用量
    short_margin_used: Dict[str, float] = field(default_factory=dict)  # ticker -> margin used
    margin_requirement: float = 0.5  # 保证金要求比例，默认50%
    trade_history: List[TradeRecord] = field(default_factory=list)
    daily_values: List[Dict[str, Any]] = field(default_factory=list)

    def __post_init__(self):
        if self.cash == 0.0:
            self.cash = self.initial_capital


class ParallelLine:
    """单条平行线交易线"""

    def __init__(self, agent_name: str, initial_capital: float, tickers: List[str]):
        self.agent_name = agent_name
        self.tickers = tickers
        self.portfolio = ParallelLinePortfolio(
            agent_name=agent_name,
            initial_capital=initial_capital
        )

        # 初始化持仓
        for ticker in tickers:
            self.portfolio.positions[ticker] = 0
            self.portfolio.cost_basis[ticker] = 0.0
            self.portfolio.short_positions[ticker] = 0
            self.portfolio.short_cost_basis[ticker] = 0.0
            self.portfolio.short_margin_used[ticker] = 0.0

    def execute_trade(
        self,
        ticker: str,
        action: str,
        quantity: int,
        price: float,
        date: str,
        reasoning: str = "",
        position_sizing: float = 0.0
    ) -> bool:
        """
        执行交易

        Args:
            ticker: 股票代码
            action: 交易动作 (buy/sell/short/cover/hold)
            quantity: 交易数量
            price: 交易价格
            date: 交易日期
            reasoning: 交易理由

        Returns:
            bool: 交易是否成功执行
        """
        # 确保ticker在投资组合中初始化
        if ticker not in self.portfolio.positions:
            self.portfolio.positions[ticker] = 0
            self.portfolio.cost_basis[ticker] = 0.0
            self.portfolio.short_positions[ticker] = 0
            self.portfolio.short_cost_basis[ticker] = 0.0
            self.portfolio.short_margin_used[ticker] = 0.0
        if action == "hold" or (quantity <= 0 and action != "sell"):
            # 为hold决策和无效数量的交易决策创建交易记录
            # 注意：sell操作即使quantity <= 0也要继续处理，因为可能需要转换为short
            if action == "hold":
                hold_trade = TradeRecord(
                    date=date,
                    ticker=ticker,
                    action="hold",
                    quantity=0,
                    price=price,
                    total_value=0.0,
                    agent_name=self.agent_name,
                    reasoning=reasoning
                )
                self.portfolio.trade_history.append(hold_trade)
            elif quantity <= 0:
                # 为数量为0或负数的交易信号创建记录
                zero_quantity_trade = TradeRecord(
                    date=date,
                    ticker=ticker,
                    action=f"{action}_zero_quantity",
                    quantity=0,
                    price=price,
                    total_value=0.0,
                    agent_name=self.agent_name,
                    reasoning=f"交易信号: {action}，但计算出的数量为{quantity}。{reasoning}"
                )
                self.portfolio.trade_history.append(zero_quantity_trade)
            return True

        total_value = quantity * price

        if action == "buy":
            # 检查现金是否足够
            if self.portfolio.cash < total_value:
                # 调整购买数量到可承受范围
                affordable_quantity = int(self.portfolio.cash / price)
                if affordable_quantity <= 0:
                    return False
                quantity = affordable_quantity
                total_value = quantity * price

            # 执行买入
            self.portfolio.cash -= total_value
            self.portfolio.positions[ticker] += quantity
            self.portfolio.cost_basis[ticker] += total_value

        elif action == "sell":
            # 检查持仓情况
            current_position = self.portfolio.positions.get(ticker, 0)

            if current_position > 0:
                # 有持仓，根据position_sizing计算实际卖出数量
                if position_sizing > 0:
                    # 基于position_sizing计算目标卖出数量
                    target_sell_quantity = int(current_position * position_sizing)
                    sell_quantity = min(target_sell_quantity, current_position)
                else:
                    # 如果没有指定position_sizing，使用传入的quantity
                    sell_quantity = min(quantity, current_position)

                if sell_quantity > 0:
                    total_value = sell_quantity * price

                    # 执行卖出
                    self.portfolio.cash += total_value
                    self.portfolio.positions[ticker] -= sell_quantity

                    # 按比例减少成本基础
                    cost_ratio = sell_quantity / current_position
                    self.portfolio.cost_basis[ticker] *= (1 - cost_ratio)

                    quantity = sell_quantity
                else:
                    # 计算出的卖出数量为0，记录为持有
                    hold_trade = TradeRecord(
                        date=date,
                        ticker=ticker,
                        action="hold",
                        quantity=0,
                        price=price,
                        total_value=0.0,
                        agent_name=self.agent_name,
                        reasoning=f"持有：计算出的卖出数量为0。{reasoning}"
                    )
                    self.portfolio.trade_history.append(hold_trade)
                    return True
            else:
                # 没有持仓，自动转换为做空操作
                print(f"💡 {self.agent_name}: 0仓位时尝试卖出{ticker}，自动转换为做空操作")

                # 计算做空数量：如果没有指定数量，使用合理的默认值
                if quantity <= 0:
                    # 基于可用现金和position_sizing计算做空数量
                    if position_sizing > 0:
                        available_margin = self.portfolio.cash
                        target_short_value = available_margin * position_sizing
                        quantity = max(1, int(target_short_value / price / self.portfolio.margin_requirement))
                    else:
                        # 默认做空少量股票
                        quantity = max(1, int(self.portfolio.cash * 0.1 / price / self.portfolio.margin_requirement))

                # 递归调用做空操作
                return self.execute_trade(
                    ticker=ticker,
                    action="short",
                    quantity=quantity,
                    price=price,
                    date=date,
                    reasoning=f"自动转换：0仓位卖出转为做空。原因：{reasoning}",
                    position_sizing=position_sizing
                )

        elif action == "short":
            # 做空操作
            proceeds = quantity * price
            margin_required = proceeds * self.portfolio.margin_requirement

            if margin_required <= self.portfolio.cash:
                # 有足够保证金，执行做空
                # 更新做空持仓的加权平均成本基础
                old_short_shares = self.portfolio.short_positions.get(ticker, 0)
                old_cost_basis = self.portfolio.short_cost_basis.get(ticker, 0)
                total_shares = old_short_shares + quantity

                if total_shares > 0:
                    total_old_cost = old_cost_basis * old_short_shares
                    total_new_cost = price * quantity
                    self.portfolio.short_cost_basis[ticker] = (total_old_cost + total_new_cost) / total_shares

                # 更新持仓和保证金
                self.portfolio.short_positions[ticker] += quantity
                self.portfolio.short_margin_used[ticker] += margin_required
                self.portfolio.margin_used += margin_required

                # 收到做空收益，但需要扣除保证金
                self.portfolio.cash += proceeds - margin_required
                total_value = proceeds
            else:
                # 保证金不足，计算最大可做空数量
                max_quantity = int(self.portfolio.cash / (price * self.portfolio.margin_requirement))
                if max_quantity > 0:
                    quantity = max_quantity
                    proceeds = quantity * price
                    margin_required = proceeds * self.portfolio.margin_requirement

                    old_short_shares = self.portfolio.short_positions.get(ticker, 0)
                    old_cost_basis = self.portfolio.short_cost_basis.get(ticker, 0)
                    total_shares = old_short_shares + quantity

                    if total_shares > 0:
                        total_old_cost = old_cost_basis * old_short_shares
                        total_new_cost = price * quantity
                        self.portfolio.short_cost_basis[ticker] = (total_old_cost + total_new_cost) / total_shares

                    self.portfolio.short_positions[ticker] += quantity
                    self.portfolio.short_margin_used[ticker] += margin_required
                    self.portfolio.margin_used += margin_required
                    self.portfolio.cash += proceeds - margin_required
                    total_value = proceeds
                else:
                    # 无法做空，记录失败
                    failed_trade = TradeRecord(
                        date=date,
                        ticker=ticker,
                        action="short_failed",
                        quantity=0,
                        price=price,
                        total_value=0.0,
                        agent_name=self.agent_name,
                        reasoning=f"做空失败：保证金不足。{reasoning}"
                    )
                    self.portfolio.trade_history.append(failed_trade)
                    return False

        elif action == "cover":
            # 平仓操作
            current_short_position = self.portfolio.short_positions.get(ticker, 0)

            if current_short_position > 0:
                # 有做空持仓，根据position_sizing计算实际平仓数量
                if position_sizing > 0:
                    target_cover_quantity = int(current_short_position * position_sizing)
                    cover_quantity = min(target_cover_quantity, current_short_position)
                else:
                    cover_quantity = min(quantity, current_short_position)

                if cover_quantity > 0:
                    cover_cost = cover_quantity * price
                    avg_short_price = self.portfolio.short_cost_basis.get(ticker, 0)

                    # 计算平仓盈亏（做空盈利 = 开仓价 - 平仓价）
                    realized_gain = (avg_short_price - price) * cover_quantity

                    # 计算释放的保证金
                    if current_short_position > 0:
                        portion = cover_quantity / current_short_position
                    else:
                        portion = 1.0

                    margin_to_release = portion * self.portfolio.short_margin_used.get(ticker, 0)

                    # 更新持仓
                    self.portfolio.short_positions[ticker] -= cover_quantity
                    self.portfolio.short_margin_used[ticker] -= margin_to_release
                    self.portfolio.margin_used -= margin_to_release

                    # 支付平仓成本，但释放保证金
                    self.portfolio.cash += margin_to_release - cover_cost

                    # 如果完全平仓，重置成本基础
                    if self.portfolio.short_positions[ticker] == 0:
                        self.portfolio.short_cost_basis[ticker] = 0.0
                        self.portfolio.short_margin_used[ticker] = 0.0

                    quantity = cover_quantity
                    total_value = cover_cost
                else:
                    # 计算出的平仓数量为0，记录为持有
                    hold_trade = TradeRecord(
                        date=date,
                        ticker=ticker,
                        action="hold",
                        quantity=0,
                        price=price,
                        total_value=0.0,
                        agent_name=self.agent_name,
                        reasoning=f"持有：计算出的平仓数量为0。{reasoning}"
                    )
                    self.portfolio.trade_history.append(hold_trade)
                    return True
            else:
                # 没有做空持仓，无法平仓
                failed_trade = TradeRecord(
                    date=date,
                    ticker=ticker,
                    action="cover_failed",
                    quantity=0,
                    price=price,
                    total_value=0.0,
                    agent_name=self.agent_name,
                    reasoning=f"平仓失败：没有{ticker}做空持仓。{reasoning}"
                )
                self.portfolio.trade_history.append(failed_trade)
                return False

        # 记录交易
        trade_record = TradeRecord(
            date=date,
            ticker=ticker,
            action=action,
            quantity=quantity,
            price=price,
            total_value=total_value,
            agent_name=self.agent_name,
            reasoning=reasoning
        )
        self.portfolio.trade_history.append(trade_record)

        return True

    def calculate_portfolio_value(self, current_prices: Dict[str, float]) -> float:
        """计算当前投资组合总价值"""
        total_value = self.portfolio.cash

        # 计算多头持仓价值
        for ticker, quantity in self.portfolio.positions.items():
            if quantity != 0 and ticker in current_prices:
                total_value += quantity * current_prices[ticker]

        # 计算做空持仓价值（做空的未实现盈亏）
        for ticker, short_quantity in self.portfolio.short_positions.items():
            if short_quantity != 0 and ticker in current_prices:
                # 做空的未实现盈亏 = (开仓价 - 当前价) * 数量
                avg_short_price = self.portfolio.short_cost_basis.get(ticker, 0)
                unrealized_pnl = (avg_short_price - current_prices[ticker]) * short_quantity
                total_value += unrealized_pnl

        return total_value

    def record_daily_value(self, date: str, current_prices: Dict[str, float]):
        """记录每日投资组合价值"""
        portfolio_value = self.calculate_portfolio_value(current_prices)

        daily_record = {
            "date": date,
            "portfolio_value": portfolio_value,
            "cash": self.portfolio.cash,
            "positions": dict(self.portfolio.positions),
            "short_positions": dict(self.portfolio.short_positions),
            "margin_used": self.portfolio.margin_used,
            "current_prices": dict(current_prices)
        }

        self.portfolio.daily_values.append(daily_record)

    def get_performance_metrics(self) -> Dict[str, Any]:
        """计算性能指标"""
        if len(self.portfolio.daily_values) < 2:
            return {
                "total_return": 0.0,
                "annualized_return": 0.0,
                "sharpe_ratio": 0.0,
                "max_drawdown": 0.0,
                "total_trades": len(self.portfolio.trade_history),
                "win_rate": 0.0
            }

        # 计算每日收益率
        values = [record["portfolio_value"] for record in self.portfolio.daily_values]
        returns = np.diff(values) / values[:-1]

        # 总收益率
        total_return = (values[-1] - self.portfolio.initial_capital) / self.portfolio.initial_capital

        # 年化收益率（假设252个交易日）
        days = len(values)
        annualized_return = (1 + total_return) ** (252 / days) - 1 if days > 0 else 0

        # 夏普比率（假设无风险利率为0）
        if len(returns) > 1 and np.std(returns) > 0:
            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252)
        else:
            sharpe_ratio = 0.0

        # 最大回撤
        peak = np.maximum.accumulate(values)
        drawdown = (values - peak) / peak
        max_drawdown = np.min(drawdown)

        # 胜率计算
        profitable_trades = 0
        total_trades = 0

        for trade in self.portfolio.trade_history:
            if trade.action in ["sell"]:
                total_trades += 1
                # 简化的盈利判断（实际应该考虑买入成本）
                if trade.total_value > 0:
                    profitable_trades += 1

        win_rate = profitable_trades / total_trades if total_trades > 0 else 0.0

        return {
            "total_return": total_return,
            "annualized_return": annualized_return,
            "sharpe_ratio": sharpe_ratio,
            "max_drawdown": abs(max_drawdown),
            "total_trades": len(self.portfolio.trade_history),
            "win_rate": win_rate,
            "final_value": values[-1],
            "initial_capital": self.portfolio.initial_capital
        }


class ParallelTradingManager:
    """平行线交易管理器"""

    def __init__(
        self,
        initial_capital: float,
        tickers: List[str],
        participating_agents: List[str]
    ):
        self.initial_capital = initial_capital
        self.tickers = tickers
        self.participating_agents = participating_agents
        self.parallel_lines: Dict[str, ParallelLine] = {}

        # 初始化推理日志保存相关变量
        self.reasoning_logs_dir = None
        self.save_reasoning = False

        # 为每个参与的代理创建平行线，每个代理都获得完整的初始资本
        for agent_name in participating_agents:
            self.parallel_lines[agent_name] = ParallelLine(
                agent_name=agent_name,
                initial_capital=initial_capital,  # 每个代理都获得完整的初始资本
                tickers=tickers
            )

    def set_reasoning_logs_config(self, reasoning_logs_dir: str, save_reasoning: bool):
        """设置推理日志保存配置"""
        self.reasoning_logs_dir = reasoning_logs_dir
        self.save_reasoning = save_reasoning

    def execute_parallel_trades(
        self,
        signals: Dict[str, Dict[str, ParallelTradingSignal]],
        current_prices: Dict[str, float],
        current_date: str
    ) -> Dict[str, List[TradeRecord]]:
        """
        执行所有平行线的交易

        Args:
            signals: 代理信号 {agent_name: {ticker: signal}}
            current_prices: 当前价格 {ticker: price}
            current_date: 当前日期

        Returns:
            Dict[str, List[TradeRecord]]: 每条线的交易记录
        """
        executed_trades = {}

        for agent_name, parallel_line in self.parallel_lines.items():
            agent_trades = []

            if agent_name in signals:
                agent_signals = signals[agent_name]

                for ticker in self.tickers:
                    if ticker in agent_signals and ticker in current_prices:
                        signal = agent_signals[ticker]

                        # 保存并行代理推理日志
                        self._save_parallel_agent_reasoning(
                            signal, agent_name, ticker, current_date
                        )

                        # 执行交易
                        success = parallel_line.execute_trade(
                            ticker=ticker,
                            action=signal.trading_action,
                            quantity=signal.trading_quantity,
                            price=current_prices[ticker],
                            date=current_date,
                            reasoning=signal.reasoning,
                            position_sizing=signal.position_sizing
                        )

                        # 记录所有交易决策（现在execute_trade已经处理了hold决策）
                        if success:
                            # 获取最新的交易记录
                            if parallel_line.portfolio.trade_history:
                                latest_trade = parallel_line.portfolio.trade_history[-1]
                                agent_trades.append(latest_trade)
            else:
                # 如果代理没有生成信号，为每个ticker记录默认的hold操作
                for ticker in self.tickers:
                    if ticker in current_prices:
                        success = parallel_line.execute_trade(
                            ticker=ticker,
                            action="hold",
                            quantity=0,
                            price=current_prices[ticker],
                            date=current_date,
                            reasoning=f"代理 {agent_name} 没有生成交易信号，默认保持持有状态",
                            position_sizing=0.0
                        )

                        if success and parallel_line.portfolio.trade_history:
                            latest_trade = parallel_line.portfolio.trade_history[-1]
                            agent_trades.append(latest_trade)

            executed_trades[agent_name] = agent_trades

            # 记录每日价值
            parallel_line.record_daily_value(current_date, current_prices)

        return executed_trades

    def get_performance_comparison(self, main_system_results: dict = None) -> pd.DataFrame:
        """获取所有平行线的性能对比，包括主回测系统作为基准"""
        performance_data = []

        for agent_name, parallel_line in self.parallel_lines.items():
            metrics = parallel_line.get_performance_metrics()
            metrics["agent_name"] = agent_name
            performance_data.append(metrics)

        # 添加主回测系统作为基准
        if main_system_results:
            main_metrics = {
                "agent_name": "main_system_portfolio_manager",
                "final_value": main_system_results.get("total_value", 100000.0),
                "total_return": main_system_results.get("return_pct", 0.0),
                "sharpe_ratio": main_system_results.get("sharpe_ratio", 0.0),
                "sortino_ratio": main_system_results.get("sortino_ratio", 0.0),
                "max_drawdown": main_system_results.get("max_drawdown", 0.0),
                "total_trades": main_system_results.get("total_trades", 0),
                "win_rate": main_system_results.get("win_rate", 0.0),
                "avg_trade_return": main_system_results.get("avg_trade_return", 0.0),
                "volatility": main_system_results.get("volatility", 0.0),
                "cash_balance": main_system_results.get("cash_balance", 0.0),
                "position_value": main_system_results.get("total_position_value", 0.0)
            }
            performance_data.append(main_metrics)

        return pd.DataFrame(performance_data)

    def get_detailed_performance_report(self) -> Dict[str, Any]:
        """获取详细的性能报告"""
        report = {
            "summary": {},
            "individual_performance": {},
            "trade_analysis": {},
            "portfolio_evolution": {}
        }

        # 汇总统计
        all_metrics = []
        for parallel_line in self.parallel_lines.values():
            all_metrics.append(parallel_line.get_performance_metrics())

        if all_metrics:
            report["summary"] = {
                "best_performer": max(all_metrics, key=lambda x: x["total_return"]),
                "worst_performer": min(all_metrics, key=lambda x: x["total_return"]),
                "average_return": np.mean([m["total_return"] for m in all_metrics]),
                "average_sharpe": np.mean([m["sharpe_ratio"] for m in all_metrics])
            }

        # 个体性能
        for agent_name, parallel_line in self.parallel_lines.items():
            report["individual_performance"][agent_name] = parallel_line.get_performance_metrics()

        return report

    def save_results(self, output_dir: str, experiment_name: str, reasoning_dir: str = None, main_system_results: dict = None):
        """保存平行线交易结果，为每个平行线程创建独立的文件夹"""
        import os
        import shutil

        # 创建主输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 保存性能对比（汇总文件）
        performance_df = self.get_performance_comparison(main_system_results)
        performance_df.to_csv(
            os.path.join(output_dir, f"{experiment_name}_parallel_performance.csv"),
            index=False
        )

        # 保存详细报告（汇总文件）
        detailed_report = self.get_detailed_performance_report()
        with open(os.path.join(output_dir, f"{experiment_name}_detailed_report.json"), "w", encoding="utf-8") as f:
            json.dump(detailed_report, f, indent=2, ensure_ascii=False, default=str)

        # 为主回测系统创建文件夹（如果提供了结果）
        if main_system_results:
            main_system_dir = os.path.join(output_dir, "parallel_thread_main_system_portfolio_manager")
            os.makedirs(main_system_dir, exist_ok=True)

            # 保存主系统性能指标
            with open(os.path.join(main_system_dir, "main_system_portfolio_manager_performance.json"), "w", encoding="utf-8") as f:
                json.dump(main_system_results, f, indent=2, ensure_ascii=False, default=str)

            # 创建一个简化的交易记录文件（主要用于显示基准信息）
            main_system_trades = [{
                "date": "N/A",
                "ticker": "BENCHMARK",
                "action": "benchmark",
                "quantity": 0,
                "price": 0.0,
                "total_value": main_system_results.get("total_value", 0.0),
                "reasoning": f"Traditional centralized portfolio management - Final Value: ${main_system_results.get('total_value', 0.0):.2f}, Return: {main_system_results.get('return_pct', 0.0)*100:.2f}%"
            }]

            main_trades_df = pd.DataFrame(main_system_trades)
            main_trades_df.to_csv(
                os.path.join(main_system_dir, "main_system_portfolio_manager_trades.csv"),
                index=False
            )

        # 为每个平行线程创建独立的文件夹
        for agent_name, parallel_line in self.parallel_lines.items():
            # 创建代理专用目录
            agent_dir = os.path.join(output_dir, f"parallel_thread_{agent_name}")
            os.makedirs(agent_dir, exist_ok=True)

            # 保存交易历史
            trades_data = []
            for trade in parallel_line.portfolio.trade_history:
                trades_data.append({
                    "date": trade.date,
                    "ticker": trade.ticker,
                    "action": trade.action,
                    "quantity": trade.quantity,
                    "price": trade.price,
                    "total_value": trade.total_value,
                    "reasoning": trade.reasoning
                })

            trades_df = pd.DataFrame(trades_data)

            # 如果没有交易记录，创建一个包含默认hold记录的CSV文件
            if trades_df.empty:
                # 为没有任何交易活动的代理创建默认的hold记录
                default_trades = []
                if parallel_line.portfolio.daily_values:
                    # 基于每日价值记录创建hold记录
                    for daily_record in parallel_line.portfolio.daily_values:
                        for ticker in daily_record.get('current_prices', {}):
                            default_trades.append({
                                "date": daily_record['date'],
                                "ticker": ticker,
                                "action": "hold",
                                "quantity": 0,
                                "price": daily_record['current_prices'][ticker],
                                "total_value": 0.0,
                                "reasoning": f"代理 {agent_name} 在此日期没有生成交易信号，默认保持持有状态"
                            })

                if default_trades:
                    trades_df = pd.DataFrame(default_trades)

            # 始终保存trades.csv文件，即使是空的或默认记录
            trades_df.to_csv(
                os.path.join(agent_dir, f"{agent_name}_trades.csv"),
                index=False
            )

            # 保存个体性能指标
            individual_metrics = parallel_line.get_performance_metrics()
            with open(os.path.join(agent_dir, f"{agent_name}_performance.json"), "w", encoding="utf-8") as f:
                json.dump(individual_metrics, f, indent=2, ensure_ascii=False, default=str)

            # 保存投资组合演变历史
            portfolio_history = []
            for daily_value in parallel_line.portfolio.daily_values:
                portfolio_history.append(daily_value)

            if portfolio_history:
                portfolio_df = pd.DataFrame(portfolio_history)
                portfolio_df.to_csv(
                    os.path.join(agent_dir, f"{agent_name}_portfolio_evolution.csv"),
                    index=False
                )

            # 注意：并行代理的推理日志现在直接保存到代理目录，无需从主实验目录复制
            # 推理日志已经在 execute_parallel_trades 方法中直接保存到正确位置

        print(f"📁 平行交易结果已保存到: {output_dir}")
        print(f"   - 汇总报告: {experiment_name}_parallel_performance.csv")
        print(f"   - 详细报告: {experiment_name}_detailed_report.json")
        for agent_name in self.parallel_lines.keys():
            print(f"   - {agent_name}: parallel_thread_{agent_name}/")

    def _copy_agent_reasoning_logs(self, reasoning_dir: str, agent_dir: str, agent_name: str):
        """复制特定代理的推理日志到其专用目录"""
        import glob
        import re

        # 创建推理日志子目录
        agent_reasoning_dir = os.path.join(agent_dir, "reasoning_logs")
        os.makedirs(agent_reasoning_dir, exist_ok=True)

        # 查找所有推理日志文件
        pattern = os.path.join(reasoning_dir, "**", "*.json")
        all_files = glob.glob(pattern, recursive=True)

        # 过滤出只包含该代理的并行推理日志
        # 文件名格式: YYYY-MM-DD_TICKER_agent_name_parallel_timestamp.json
        parallel_pattern = re.compile(rf".*_{re.escape(agent_name)}_parallel_\d+\.json$")

        matching_files = [f for f in all_files if parallel_pattern.search(os.path.basename(f))]

        # 复制匹配的文件
        for file_path in matching_files:
            if os.path.isfile(file_path):
                filename = os.path.basename(file_path)
                dest_path = os.path.join(agent_reasoning_dir, filename)
                try:
                    shutil.copy2(file_path, dest_path)
                except Exception as e:
                    print(f"警告: 无法复制推理日志 {filename}: {e}")

        # 查找错误日志 - 也只查找并行代理的错误日志
        error_dir = os.path.join(reasoning_dir, "errors")
        if os.path.exists(error_dir):
            error_pattern = os.path.join(error_dir, "*.json")
            error_files = glob.glob(error_pattern)

            parallel_error_pattern = re.compile(rf".*_{re.escape(agent_name)}_parallel.*\.json$")
            matching_error_files = [f for f in error_files if parallel_error_pattern.search(os.path.basename(f))]

            if matching_error_files:
                agent_errors_dir = os.path.join(agent_dir, "errors")
                os.makedirs(agent_errors_dir, exist_ok=True)

                for error_file in matching_error_files:
                    if os.path.isfile(error_file):
                        filename = os.path.basename(error_file)
                        dest_path = os.path.join(agent_errors_dir, filename)
                        try:
                            shutil.copy2(error_file, dest_path)
                        except Exception as e:
                            print(f"警告: 无法复制错误日志 {filename}: {e}")

        # 查找重试错误日志 - 也只查找并行代理的重试错误日志
        retry_dir = os.path.join(reasoning_dir, "retry_errors")
        if os.path.exists(retry_dir):
            retry_pattern = os.path.join(retry_dir, "*.json")
            retry_files = glob.glob(retry_pattern)

            parallel_retry_pattern = re.compile(rf".*_{re.escape(agent_name)}_parallel.*\.json$")
            matching_retry_files = [f for f in retry_files if parallel_retry_pattern.search(os.path.basename(f))]

            if matching_retry_files:
                agent_retry_dir = os.path.join(agent_dir, "retry_errors")
                os.makedirs(agent_retry_dir, exist_ok=True)

                for retry_file in matching_retry_files:
                    if os.path.isfile(retry_file):
                        filename = os.path.basename(retry_file)
                        dest_path = os.path.join(agent_retry_dir, filename)
                        try:
                            shutil.copy2(retry_file, dest_path)
                        except Exception as e:
                            print(f"警告: 无法复制重试错误日志 {filename}: {e}")

    def _save_parallel_agent_reasoning(self, signal, agent_name: str, ticker: str, current_date: str):
        """直接保存并行代理的推理日志到 parallel_trading_results 目录"""
        if not self.save_reasoning or not self.reasoning_logs_dir:
            return

        import json
        import os
        from datetime import datetime

        # 定义本地的序列化函数
        def convert_to_serializable(obj):
            if hasattr(obj, "to_dict"):  # Handle Pandas Series/DataFrame
                return obj.to_dict()
            elif hasattr(obj, "dict"):  # Handle Pydantic models
                return obj.dict()
            elif hasattr(obj, "__dict__"):  # Handle custom objects
                return obj.__dict__
            elif isinstance(obj, (int, float, bool, str)):
                return obj
            elif isinstance(obj, (list, tuple)):
                return [convert_to_serializable(item) for item in obj]
            elif isinstance(obj, dict):
                return {key: convert_to_serializable(value) for key, value in obj.items()}
            else:
                return str(obj)  # Fallback to string representation

        # 创建 parallel_trading_results 目录结构
        parallel_results_dir = "parallel_trading_results"
        agent_dir = os.path.join(parallel_results_dir, f"parallel_thread_{agent_name}")
        reasoning_logs_dir = os.path.join(agent_dir, "reasoning_logs")
        os.makedirs(reasoning_logs_dir, exist_ok=True)

        # 创建文件名
        timestamp = datetime.now().strftime("%H%M%S%f")[:-3]  # Include milliseconds
        filename = f"{current_date}_{ticker}_{agent_name}_parallel_{timestamp}.json"
        filepath = os.path.join(reasoning_logs_dir, filename)

        # 准备要保存的数据
        reasoning_data = {
            "experiment_date": current_date,
            "ticker": ticker,
            "agent_name": f"{agent_name}_parallel",
            "timestamp": datetime.now().isoformat(),
            "reasoning": convert_to_serializable(signal)
        }

        # 保存到JSON文件
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(reasoning_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存并行代理推理日志时出错: {e}")
